/**
 * WCAG Rule 7: Focus Visible - 2.4.7
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FocusTracker } from '../utils/focus-tracker';
import { AdvancedFocusTracker } from '../utils/advanced-focus-tracker';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface FocusVisibleConfig extends EnhancedCheckConfig {
  enableAdvancedFocusTracking?: boolean;
  enableCustomIndicatorDetection?: boolean;
  enableContrastMeasurement?: boolean;
  enableFrameworkOptimization?: boolean;
  enableThirdPartyLibraries?: boolean;
  enableWideGamutAnalysis?: boolean;
  enableAdvancedColorSpaces?: boolean;
  enableKeyboardTrapping?: boolean;
}

export class FocusVisibleCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedCheckTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private advancedFocusTracker = AdvancedFocusTracker.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();
  private focusTracker = new FocusTracker();

  /**
   * Perform focus visible check - 100% automated with enhanced evidence
   */
  async performCheck(config: FocusVisibleConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: FocusVisibleConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableAdvancedFocusTracking: true,
      enableCustomIndicatorDetection: true,
      enableContrastMeasurement: true,
      enableFrameworkOptimization: true,
      enableThirdPartyLibraries: true,
      enableWideGamutAnalysis: true,
      enableAdvancedColorSpaces: true,
      enableKeyboardTrapping: true,
    };

    const result = await this.enhancedCheckTemplate.executeEnhancedCheck(
      'WCAG-007',
      'Focus Visible',
      'operable',
      0.09,
      'AA',
      enhancedConfig,
      this.executeFocusVisibleCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with focus-specific analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-007',
        ruleName: 'Focus Visible',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'focus-visibility-analysis',
          focusTracking: true,
          visualIndicators: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85, // High threshold for focus visibility
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Perform enhanced focus visible check with axe-core validation
   */
  async performEnhancedCheck(config: EnhancedCheckConfig) {
    return this.enhancedCheckTemplate.executeEnhancedCheck(
      'WCAG-007',
      'Focus Visible',
      'operable',
      0.09,
      'AA',
      config,
      this.executeFocusVisibleCheck.bind(this),
      true, // Requires browser
      false, // No manual review
      {
        enableAxeValidation: config.enableAxeValidation !== false, // Default to true
        axeRules: ['focus-order-semantics', 'focusable-content'],
        mergeStrategy: 'supplement'
      }
    );
  }

  /**
   * Execute focus visibility analysis
   */
  private async executeFocusVisibleCheck(page: Page, config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced focus visibility analysis using AdvancedFocusTracker
    try {
      const advancedFocusAnalysis = await this.advancedFocusTracker.analyzeAdvancedFocusVisibility(page);

      // Add enhanced evidence from advanced focus analysis
      evidence.push({
        type: 'advanced-focus-visibility-analysis',
        description: 'Advanced focus visibility analysis with custom indicator detection',
        impact: advancedFocusAnalysis.analysis.hasIssues ? 'critical' : 'pass',
        element: 'focusable-elements',
        value: JSON.stringify({
          analysis: advancedFocusAnalysis.analysis,
          customIndicators: advancedFocusAnalysis.customIndicators,
          thirdPartyEnhanced: advancedFocusAnalysis.thirdPartyEnhanced,
        }),
        recommendation: advancedFocusAnalysis.analysis.hasIssues
          ? 'Ensure all focusable elements have visible focus indicators'
          : 'All focusable elements have appropriate focus indicators',
      });

      // Collect issues and recommendations from advanced analysis
      if (advancedFocusAnalysis.analysis.hasIssues) {
        issues.push(...advancedFocusAnalysis.analysis.issues);
        recommendations.push(...advancedFocusAnalysis.analysis.recommendations);
      }

    } catch (error) {
      console.warn('Advanced focus visibility analysis failed, falling back to basic analysis:', error);
    }

    // Get all focusable elements - Basic fallback analysis
    const focusableElements = await FocusTracker.getFocusableElements(page);

    let totalElements = 0;
    let passedElements = 0;

    // Test focus visibility for each element
    for (const element of focusableElements) {
      totalElements++;

      const focusIndicator = await FocusTracker.analyzeFocusVisibility(page, element);

      if (focusIndicator.hasVisibleIndicator) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: 'Focus indicator is visible and meets requirements',
          value: `Type: ${focusIndicator.indicatorType}, Contrast: ${focusIndicator.contrastRatio || 'N/A'}:1`,
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Focus indicator not visible on ${element.selector}`);

        evidence.push({
          type: 'measurement',
          description: 'Focus indicator fails visibility requirements',
          value: focusIndicator.recommendation || 'No visible focus indicator',
          selector: element.selector,
          severity: 'error',
        });

        if (focusIndicator.recommendation) {
          recommendations.push(`${element.selector}: ${focusIndicator.recommendation}`);
        }
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Focus visibility analysis summary',
      value: `${passedElements}/${totalElements} focusable elements have visible focus indicators`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift('Add visible focus indicators to all focusable elements');
      recommendations.push('Ensure focus indicators have sufficient contrast (3:1 minimum)');
      recommendations.push('Use outline, border, or background changes for focus indication');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
